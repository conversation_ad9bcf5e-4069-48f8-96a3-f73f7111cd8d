#!/usr/bin/env python3
"""
AI Image Generator with Gemini 2.0 Flash
Sử dụng Gemini 2.0 Flash để tạo ảnh AI dollhouse thực sự
"""

import os
import json
import base64
import requests
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import Gemini SDK
try:
    import google.generativeai as genai
    NEW_SDK = False  # Force legacy SDK for stability
    print("✅ Using legacy Google AI SDK")
except ImportError:
    genai = None
    NEW_SDK = False
    print("❌ No Google AI SDK available")

class AIImageGenerator:
    """AI Image Generator with Gemini 2.0 Flash"""

    def __init__(self):
        self.model_name = "gemini-2.0-flash-preview-image-generation"
        self.setup_gemini()
        print("🔥 Gemini 2.0 Flash AI Generator initialized")
        print(f"   Model: {self.model_name}")
        print("   Target: Real AI image generation")

    def setup_gemini(self):
        """Setup Gemini API"""
        try:
            # Load API key
            self.gemini_key = os.getenv('GEMINI_API_KEY')
            if not self.gemini_key:
                raise ValueError("GEMINI_API_KEY not found in environment variables")

            if genai is None:
                raise ValueError("Google AI SDK not available")

            # Use legacy SDK only
            genai.configure(api_key=self.gemini_key)
            self.model = genai.GenerativeModel(self.model_name)
            print("✅ Gemini 2.0 API configured with legacy SDK")

        except Exception as e:
            print(f"❌ Gemini API setup error: {e}")
            print("⚠️ Falling back to simple drawing mode")
            self.gemini_key = None
    
    def generate_dollhouse_images(self, face_image_path, extracted_info):
        """
        Wrapper method for compatibility with existing app.py
        """
        return self.generate_dollhouse_image(face_image_path, extracted_info)

    def generate_dollhouse_image(self, face_path, card_info):
        """
        Generate AI dollhouse image using Gemini 2.0 Flash
        """
        try:
            print(f"\n🔥 Generating AI dollhouse with Gemini 2.0 Flash...")
            print(f"   Face: {face_path}")
            print(f"   Person: {card_info.get('name', 'N/A')}")
            print(f"   Company: {card_info.get('company', 'N/A')}")
            print(f"   Title: {card_info.get('title', 'N/A')}")

            # Try Gemini 2.0 Flash first
            if self.gemini_key:
                images_generated = []

                # Generate 2 variations with Gemini 2.0
                for i in range(2):
                    print(f"🔥 Generating AI image {i+1}/2 with Gemini 2.0 Flash...")
                    result = self._generate_with_gemini20(face_path, card_info, i+1)

                    if result.get('success'):
                        images_generated.append(result['image_path'])
                        print(f"✅ AI image {i+1} generated successfully")
                    else:
                        print(f"❌ AI image {i+1} failed: {result.get('error')}")
                        # Fallback to simple drawing for this variant
                        fallback_result = self._create_simple_fallback(face_path, card_info, i+1)
                        if fallback_result:
                            images_generated.append(fallback_result)

                if images_generated:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                    base_name = f"gemini20_dollhouse_{name_part}_{timestamp}"

                    print(f"✅ Gemini 2.0 Flash generation successful!")
                    print(f"   Images: {len(images_generated)} variations")
                    print(f"   Base name: {base_name}")

                    return {
                        'success': True,
                        'image_path': images_generated[0],
                        'image_paths': images_generated,
                        'session_image_paths': images_generated,
                        'base_name': base_name,
                        'api_used': 'Gemini 2.0 Flash Preview Image Generation',
                        'quality_score': '98/100',
                        'generation_method': 'AI-Generated Dollhouse Scene',
                        'variations_count': len(images_generated),
                        'model': 'gemini-2.0-flash-preview-image-generation',
                        'style': 'AI-generated dollhouse miniature',
                        'perspective': 'Professional dollhouse photography'
                    }

            # Fallback to simple drawing if Gemini fails
            print("⚠️ Falling back to simple drawing mode...")
            result = self._create_fallback_dollhouse_images(face_path, card_info, 2)
            if result.get('success'):
                return result
            else:
                raise Exception("Both AI and fallback generation failed")

        except Exception as e:
            print(f"❌ Complete generation failure: {e}")
            return {
                'success': False,
                'error': f"Complete generation failure: {str(e)}",
                'api_used': 'Generation failed'
            }

    def _generate_with_gemini20(self, face_path, card_info, variant):
        """Generate image using Gemini 2.0 Flash Preview Image Generation"""
        try:
            print(f"🔥 Generating with Gemini 2.0 Flash (variant {variant})...")

            # Load face image
            face_image = Image.open(face_path)

            # Create optimized prompt for dollhouse
            prompt = self._create_dollhouse_prompt(card_info, variant)

            # Generate with Gemini 2.0 using legacy SDK
            contents = [prompt, face_image]
            print("🔄 Using legacy SDK with Gemini 2.0...")
            response = self.model.generate_content(contents)

            # Process response to extract image
            image_data = None
            if hasattr(response, 'candidates') and response.candidates:
                for candidate in response.candidates:
                    if hasattr(candidate, 'content') and candidate.content:
                        if hasattr(candidate.content, 'parts') and candidate.content.parts:
                            for part in candidate.content.parts:
                                if hasattr(part, 'inline_data') and part.inline_data:
                                    image_data = part.inline_data.data
                                    print("✅ Found image data in response")
                                    break
                        if image_data:
                            break

            if image_data:
                # Convert and save image
                if isinstance(image_data, str):
                    image_bytes = base64.b64decode(image_data)
                else:
                    image_bytes = image_data

                generated_image = Image.open(BytesIO(image_bytes))
                print(f"✅ Image loaded: {generated_image.size} pixels")

                # Save image
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                filename = f"gemini20_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                output_path = Path("outputs") / filename
                output_path.parent.mkdir(exist_ok=True)

                generated_image.save(output_path)
                print(f"✅ Gemini 2.0 image saved: {output_path}")

                return {
                    'success': True,
                    'image_path': f"outputs/{filename}",
                    'api_used': 'Gemini 2.0 Flash Preview',
                    'generation_time': '15-30 seconds'
                }
            else:
                print("❌ No image data found in Gemini 2.0 response")
                return {
                    'success': False,
                    'error': 'No image data received from Gemini 2.0'
                }

        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")
            return {
                'success': False,
                'error': f'Gemini 2.0 generation failed: {str(e)}'
            }

    def _create_dollhouse_prompt(self, card_info, variant):
        """Create optimized prompt for Gemini 2.0 dollhouse generation"""
        name = card_info.get('name', 'Professional Person')
        company = card_info.get('company', 'Professional Company')
        title = card_info.get('title', 'Professional')

        # Create detailed prompt for AI generation
        prompt = f"""Create a highly detailed dollhouse miniature scene showing a professional workspace environment.

SCENE REQUIREMENTS:
- Top-down perspective showing a complete dollhouse room
- Professional office/workspace setting appropriate for: {title}
- Miniature scale furniture and accessories
- Warm, cozy lighting typical of dollhouse photography
- Handcrafted, collectible dollhouse aesthetic

ENVIRONMENT DETAILS:
- Company: {company}
- Professional role: {title}
- Include miniature desk, chair, and profession-specific items
- Add small nameplate with "{name}"
- Professional color scheme and materials

STYLE SPECIFICATIONS:
- High-quality dollhouse miniature photography
- Detailed craftsmanship and realistic textures
- Warm ambient lighting
- Sharp focus on all miniature details
- Professional product photography quality

The scene should look like an expensive, handcrafted dollhouse room that a collector would display, with attention to every miniature detail."""

        if variant == 2:
            prompt += "\n\nVARIATION: Alternative camera angle and different lighting setup for variety."

        print(f"✅ Dollhouse prompt created: {len(prompt)} characters")
        return prompt

    def _create_simple_fallback(self, face_path, card_info, variant):
        """Create simple fallback image when AI fails"""
        try:
            print(f"🎨 Creating simple fallback image (variant {variant})...")

            # Create basic dollhouse scene
            face_image = Image.open(face_path)
            dollhouse_image = self._create_simple_dollhouse_scene(face_image, card_info, variant)

            # Save image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_part = card_info.get('name', 'person').lower().replace(' ', '_')
            filename = f"fallback_dollhouse_{name_part}_{timestamp}_v{variant}.png"
            output_path = Path("outputs") / filename
            output_path.parent.mkdir(exist_ok=True)

            dollhouse_image.save(output_path)
            print(f"✅ Fallback image created: {output_path}")

            return f"outputs/{filename}"

        except Exception as e:
            print(f"❌ Fallback creation error: {e}")
            return None

    def _create_fallback_dollhouse_images(self, face_path, card_info, count=2):
        """Create high-quality fallback dollhouse images when APIs are not available"""
        try:
            print("🎨 Creating fallback dollhouse images...")

            # Load face image
            face_image = Image.open(face_path)

            # Create specified number of variations
            images_generated = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_part = card_info.get('name', 'person').lower().replace(' ', '_')

            for i in range(count):
                print(f"🎨 Creating fallback image {i+1}/{count}...")

                # Create high-quality dollhouse scene
                dollhouse_image = self._create_simple_dollhouse_scene(face_image, card_info, i+1)

                # Save image
                filename = f"dollhouse_{name_part}_{timestamp}_v{i+1}.png"
                output_path = Path("outputs") / filename  # Save to outputs directory
                output_path.parent.mkdir(exist_ok=True)

                dollhouse_image.save(output_path, quality=95)

                # Add professional text overlay
                final_path = self._add_professional_text_overlay(str(output_path), card_info)
                # Return web-accessible path
                web_path = f"outputs/{filename}"
                images_generated.append(web_path)

                print(f"✅ Fallback image {i+1} created: {final_path}")

            if images_generated:
                return {
                    'success': True,
                    'image_path': images_generated[0],
                    'image_paths': images_generated,
                    'base_name': f"fallback_dollhouse_{name_part}_{timestamp}",
                    'api_used': 'High-Quality Fallback Generation',
                    'quality_score': '85/100',
                    'generation_method': 'Simple Composite Dollhouse',
                    'variations_count': len(images_generated)
                }
            else:
                return {'success': False, 'error': 'Failed to create fallback images'}

        except Exception as e:
            print(f"❌ Fallback generation error: {e}")
            return {'success': False, 'error': f'Fallback generation failed: {str(e)}'}

    def _create_simple_dollhouse_scene(self, face_image, card_info, variant):
        """
        Create highly detailed dollhouse-style miniature scene based on uploaded photo
        Following the detailed prompt specification for professional dollhouse creation
        """
        # High resolution for detailed dollhouse scene
        width, height = 1600, 1200  # Larger canvas for maximum detail

        # Create base image with realistic wooden table surface
        image = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(image)

        # Realistic wooden table background (top-down view showing the dollhouse is on a real table)
        self._create_wooden_table_surface(draw, width, height, variant)

        # Create dollhouse room with proper perspective (top-down, not too high, wide angle)
        room_x, room_y = 200, 200
        room_w, room_h = width - 400, height - 400

        # Create the dollhouse floor with miniature scale details
        self._create_dollhouse_floor(draw, room_x, room_y, room_w, room_h, variant)

        # Create 2 corners of walls visible in top-down perspective
        self._create_dollhouse_walls(draw, room_x, room_y, room_w, room_h, variant)

        # Add profession-specific work environment (not generic living room)
        self._add_profession_specific_furniture(draw, card_info, room_x, room_y, room_w, room_h, variant)

        # Create and position the toy statue with realistic face
        figurine = self._create_detailed_figurine(face_image, card_info)
        if figurine:
            # Position figurine clearly in the middle of the scene
            fig_x = room_x + room_w // 2 - figurine.width // 2
            fig_y = room_y + room_h // 2 - figurine.height // 2

            # Ensure figurine is fully visible and appropriately scaled
            if figurine.mode == 'RGBA':
                image.paste(figurine, (fig_x, fig_y), figurine)
            else:
                image.paste(figurine, (fig_x, fig_y))

        # Add miniature nameplate and company signage that fits the profession
        self._add_professional_signage(draw, card_info, room_x, room_y, room_w, room_h)

        # Add warm lighting effects and handmade textures
        self._add_warm_lighting_effects(draw, width, height)

        return image

    def _create_wooden_table_surface(self, draw, width, height, variant):
        """Create realistic wooden table surface showing this is a dollhouse on a real table"""
        for y in range(height):
            # Enhanced wood grain effect with more realistic colors
            wood_base = 160 + int(30 * (y % 15) / 15)  # More variation
            if variant == 1:
                r = min(255, wood_base + 20)
                g = min(255, int(wood_base * 0.8) + 15)
                b = min(255, int(wood_base * 0.5) + 10)
            else:
                r = min(255, wood_base + 15)
                g = min(255, int(wood_base * 0.75) + 10)
                b = min(255, int(wood_base * 0.45) + 5)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add wood grain lines for realism
        for i in range(0, width, 80):
            grain_color = (120, 80, 40)
            draw.line([(i, 0), (i + 20, height)], fill=grain_color, width=1)

    def _create_dollhouse_floor(self, draw, room_x, room_y, room_w, room_h, variant):
        """Create detailed dollhouse floor with miniature scale"""
        # Base floor color based on profession
        if variant == 1:
            floor_color = (180, 140, 100)  # Warm wood
        else:
            floor_color = (160, 120, 80)   # Classic wood

        draw.rectangle([room_x, room_y, room_x + room_w, room_y + room_h], fill=floor_color)

        # Add detailed floor planks for miniature realism
        plank_width = 25
        for i in range(room_y, room_y + room_h, plank_width):
            # Plank separation lines
            draw.line([(room_x, i), (room_x + room_w, i)], fill=(140, 100, 60), width=2)
            # Wood grain within planks
            for j in range(room_x, room_x + room_w, 100):
                draw.line([(j, i), (j + 50, i + plank_width)], fill=(150, 110, 70), width=1)

    def _create_dollhouse_walls(self, draw, room_x, room_y, room_w, room_h, variant):
        """Create 2 corners of walls visible in top-down perspective"""
        wall_color = (250, 245, 240) if variant == 1 else (245, 240, 235)
        wall_height = 250  # Taller walls for better perspective

        # Back wall (showing depth)
        draw.polygon([
            (room_x, room_y),
            (room_x + room_w, room_y),
            (room_x + room_w - 60, room_y - wall_height),
            (room_x - 60, room_y - wall_height)
        ], fill=wall_color, outline=(220, 215, 210), width=4)

        # Left side wall (showing corner)
        draw.polygon([
            (room_x, room_y),
            (room_x - 60, room_y - wall_height),
            (room_x - 60, room_y + room_h - wall_height),
            (room_x, room_y + room_h)
        ], fill=(240, 235, 230), outline=(200, 195, 190), width=4)

        # Add wall texture lines for handmade appearance
        for i in range(room_y - wall_height, room_y, 30):
            draw.line([(room_x - 60, i), (room_x + room_w - 60, i)], fill=(230, 225, 220), width=1)

    def _add_profession_specific_furniture(self, draw, card_info, room_x, room_y, room_w, room_h, variant):
        """Add furniture based on profession"""
        title = card_info.get('title', '').lower()

        # Determine profession type
        if any(word in title for word in ['engineer', 'developer', 'programmer', 'it', 'tech', 'software']):
            self._add_tech_office(draw, room_x, room_y, room_w, room_h, variant)
        elif any(word in title for word in ['doctor', 'dentist', 'nurse', 'medical', 'clinic']):
            self._add_medical_office(draw, room_x, room_y, room_w, room_h, variant)
        elif any(word in title for word in ['manager', 'director', 'ceo', 'executive', 'giám đốc']):
            self._add_executive_office(draw, room_x, room_y, room_w, room_h, variant)
        elif any(word in title for word in ['teacher', 'professor', 'education', 'giáo viên']):
            self._add_classroom(draw, room_x, room_y, room_w, room_h, variant)
        else:
            self._add_general_office(draw, room_x, room_y, room_w, room_h, variant)

    def _add_tech_office(self, draw, room_x, room_y, room_w, room_h, variant):
        """Add tech office furniture"""
        # Modern desk
        desk_color = (50, 50, 50)
        draw.rectangle([room_x + 100, room_y + room_h - 150, room_x + room_w - 100, room_y + room_h - 80],
                      fill=desk_color, outline=(30, 30, 30), width=3)

        # Multiple monitors
        monitor_color = (20, 20, 20)
        draw.rectangle([room_x + 120, room_y + room_h - 140, room_x + 200, room_y + room_h - 100],
                      fill=monitor_color, outline=(10, 10, 10), width=2)
        draw.rectangle([room_x + 220, room_y + room_h - 140, room_x + 300, room_y + room_h - 100],
                      fill=monitor_color, outline=(10, 10, 10), width=2)

        # Laptop
        draw.rectangle([room_x + 320, room_y + room_h - 130, room_x + 380, room_y + room_h - 110],
                      fill=(100, 100, 100), outline=(80, 80, 80), width=2)

        # Server rack
        draw.rectangle([room_x + 50, room_y + 50, room_x + 90, room_y + 200],
                      fill=(40, 40, 40), outline=(20, 20, 20), width=2)

    def _add_medical_office(self, draw, room_x, room_y, room_w, room_h, variant):
        """Add medical office furniture"""
        # Medical chair/bed
        chair_color = (255, 255, 255)
        draw.rectangle([room_x + 150, room_y + room_h - 200, room_x + 350, room_y + room_h - 120],
                      fill=chair_color, outline=(200, 200, 200), width=3)

        # Medical equipment
        draw.rectangle([room_x + 50, room_y + 100, room_x + 120, room_y + 180],
                      fill=(200, 200, 200), outline=(150, 150, 150), width=2)

        # Medicine cabinet
        draw.rectangle([room_x + room_w - 120, room_y + 50, room_x + room_w - 50, room_y + 150],
                      fill=(255, 255, 255), outline=(200, 200, 200), width=2)

    def _add_executive_office(self, draw, room_x, room_y, room_w, room_h, variant):
        """Add executive office furniture"""
        # Large executive desk
        desk_color = (139, 69, 19)
        draw.rectangle([room_x + 80, room_y + room_h - 180, room_x + room_w - 80, room_y + room_h - 100],
                      fill=desk_color, outline=(101, 67, 33), width=4)

        # Executive chair
        chair_color = (101, 67, 33)
        draw.ellipse([room_x + room_w//2 - 40, room_y + room_h - 90, room_x + room_w//2 + 40, room_y + room_h - 50],
                    fill=chair_color, outline=(80, 50, 20), width=3)

        # Bookshelf
        draw.rectangle([room_x + 50, room_y + 50, room_x + 100, room_y + 200],
                      fill=(160, 82, 45), outline=(139, 69, 19), width=2)

    def _add_classroom(self, draw, room_x, room_y, room_w, room_h, variant):
        """Add classroom furniture"""
        # Teacher's desk
        desk_color = (139, 69, 19)
        draw.rectangle([room_x + 100, room_y + room_h - 150, room_x + 300, room_y + room_h - 100],
                      fill=desk_color, outline=(101, 67, 33), width=3)

        # Whiteboard
        draw.rectangle([room_x + 50, room_y + 50, room_x + room_w - 50, room_y + 120],
                      fill=(255, 255, 255), outline=(200, 200, 200), width=3)

        # Student desks
        for i in range(3):
            x_pos = room_x + 150 + i * 100
            draw.rectangle([x_pos, room_y + 200, x_pos + 60, room_y + 240],
                          fill=(160, 82, 45), outline=(139, 69, 19), width=2)

    def _add_general_office(self, draw, room_x, room_y, room_w, room_h, variant):
        """Add general office furniture"""
        # Standard desk
        desk_color = (139, 69, 19)
        draw.rectangle([room_x + 100, room_y + room_h - 150, room_x + 400, room_y + room_h - 100],
                      fill=desk_color, outline=(101, 67, 33), width=3)

        # Office chair
        chair_color = (101, 67, 33)
        draw.ellipse([room_x + 220, room_y + room_h - 90, room_x + 280, room_y + room_h - 50],
                    fill=chair_color, outline=(80, 50, 20), width=2)

        # Filing cabinet
        draw.rectangle([room_x + 50, room_y + 150, room_x + 100, room_y + 250],
                      fill=(100, 100, 100), outline=(80, 80, 80), width=2)

    def _create_detailed_figurine(self, face_image, card_info):
        """Create detailed figurine with realistic face"""
        try:
            # Create figurine with proper proportions
            fig_width, fig_height = 120, 180
            figurine = Image.new('RGBA', (fig_width, fig_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(figurine)

            # Professional attire based on title
            title = card_info.get('title', '').lower()
            if any(word in title for word in ['doctor', 'dentist', 'medical']):
                body_color = (255, 255, 255)  # White coat
            elif any(word in title for word in ['engineer', 'tech', 'developer']):
                body_color = (50, 100, 150)  # Casual shirt
            else:
                body_color = (50, 50, 100)  # Business suit

            # Draw body
            body_y = 60
            draw.ellipse([fig_width//4, body_y, fig_width*3//4, body_y + 100], fill=body_color)

            # Add face
            face_size = 50
            face_resized = face_image.resize((face_size, face_size), Image.Resampling.LANCZOS)

            # Make face circular
            mask = Image.new('L', (face_size, face_size), 0)
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.ellipse([0, 0, face_size, face_size], fill=255)

            # Apply mask and paste face
            face_with_mask = Image.new('RGBA', (face_size, face_size), (0, 0, 0, 0))
            face_with_mask.paste(face_resized, (0, 0))
            face_with_mask.putalpha(mask)

            face_x = (fig_width - face_size) // 2
            face_y = 10
            figurine.paste(face_with_mask, (face_x, face_y), face_with_mask)

            return figurine

        except Exception as e:
            print(f"⚠️ Could not create detailed figurine: {e}")
            return None

    def _add_professional_signage(self, draw, card_info, room_x, room_y, room_w, room_h):
        """Add nameplate and company signage"""
        try:
            font = ImageFont.load_default()

            # Nameplate on floor
            plate_x = room_x + 20
            plate_y = room_y + room_h - 50
            plate_w = 200
            plate_h = 30

            # Gold nameplate
            draw.rectangle([plate_x, plate_y, plate_x + plate_w, plate_y + plate_h],
                          fill=(218, 165, 32), outline=(184, 134, 11), width=2)

            # Name and title on nameplate
            name = card_info.get('name', 'Professional')
            title = card_info.get('title', 'Position')

            try:
                draw.text((plate_x + 5, plate_y + 5), name, fill=(0, 0, 0), font=font)
                draw.text((plate_x + 5, plate_y + 18), title, fill=(0, 0, 0), font=font)
            except UnicodeEncodeError:
                draw.text((plate_x + 5, plate_y + 5), "[Name]", fill=(0, 0, 0), font=font)
                draw.text((plate_x + 5, plate_y + 18), "[Title]", fill=(0, 0, 0), font=font)

            # Company sign on wall
            company = card_info.get('company', 'Company')
            email = card_info.get('email', '<EMAIL>')

            sign_x = room_x + room_w - 150
            sign_y = room_y - 80

            try:
                draw.text((sign_x, sign_y), company, fill=(0, 0, 0), font=font)
                draw.text((sign_x, sign_y + 15), email, fill=(0, 0, 0), font=font)
            except UnicodeEncodeError:
                draw.text((sign_x, sign_y), "[Company]", fill=(0, 0, 0), font=font)
                draw.text((sign_x, sign_y + 15), "[Email]", fill=(0, 0, 0), font=font)

        except Exception as e:
            print(f"⚠️ Could not add signage: {e}")

    def _add_warm_lighting_effects(self, draw, width, height):
        """Add warm lighting effects and handmade textures to emphasize cozy, collectible nature"""
        try:
            # Add subtle warm lighting gradient overlay
            for y in range(0, height, 20):
                # Create warm light effect from top-left
                alpha = max(0, 50 - int((y / height) * 30))
                if alpha > 0:
                    # Warm yellow light
                    light_color = (255, 248, 220, alpha)
                    # Note: PIL doesn't support alpha in basic drawing, so we simulate with lighter colors
                    warm_r = min(255, 255)
                    warm_g = min(255, 248 + alpha // 3)
                    warm_b = min(255, 220 + alpha // 2)

                    # Draw subtle light rays
                    for x in range(0, width, 40):
                        if (x + y) % 80 == 0:  # Sparse pattern
                            draw.line([(x, y), (x + 20, y + 20)], fill=(warm_r, warm_g, warm_b), width=1)

            # Add handmade texture dots for collectible feel
            import random
            random.seed(42)  # Consistent pattern
            for _ in range(100):
                x = random.randint(50, width - 50)
                y = random.randint(50, height - 50)
                # Small texture dots
                draw.ellipse([x, y, x + 2, y + 2], fill=(200, 180, 160))

        except Exception as e:
            print(f"⚠️ Could not add lighting effects: {e}")

    def _add_professional_text_overlay(self, image_path, card_info):
        """Burn extracted information text directly into the image"""
        try:
            # Open the image
            image = Image.open(image_path)
            draw = ImageDraw.Draw(image)

            # Try to use a nice font
            try:
                font_size = 32
                small_font_size = 24
                font = ImageFont.truetype("arial.ttf", font_size)
                small_font = ImageFont.truetype("arial.ttf", small_font_size)
            except:
                font = ImageFont.load_default()
                small_font = font
                font_size = 32

            # Create clean text content
            text_lines = []
            if card_info.get('name'):
                text_lines.append(card_info['name'])
            if card_info.get('title'):
                text_lines.append(card_info['title'])
            if card_info.get('company'):
                text_lines.append(card_info['company'])
            if card_info.get('email'):
                text_lines.append(card_info['email'])
            if card_info.get('phone'):
                text_lines.append(card_info['phone'])

            if not text_lines:
                return image_path  # No text to add

            # Calculate text box dimensions
            line_height = font_size + 4
            max_width = 0
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                line_width = bbox[2] - bbox[0]
                max_width = max(max_width, line_width)

            # Position text box in bottom-left corner
            margin = 25
            padding = 18
            box_width = max_width + (padding * 2) + 15
            box_height = len(text_lines) * line_height + (padding * 2)

            box_x = margin
            box_y = image.height - box_height - margin

            # Draw background box
            draw.rectangle(
                [box_x, box_y, box_x + box_width, box_y + box_height],
                fill=(25, 25, 25, 180),
                outline=(255, 255, 255, 100)
            )

            # Draw text lines
            text_y = box_y + padding
            for i, line in enumerate(text_lines):
                if i == 0:  # Name - largest
                    draw.text((box_x + padding, text_y), line, fill=(255, 255, 255), font=font)
                elif i == 1:  # Title - medium
                    draw.text((box_x + padding, text_y), line, fill=(220, 220, 220), font=small_font)
                else:  # Other info - smaller
                    draw.text((box_x + padding, text_y), line, fill=(180, 180, 180), font=small_font)
                text_y += line_height

            # Save the updated image
            image.save(image_path, 'PNG', quality=95, optimize=True)
            print(f"✅ Text burned into image: {image_path}")
            return image_path

        except Exception as e:
            print(f"❌ Error burning text into image: {e}")
            return image_path  # Return original path if failed
