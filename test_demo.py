#!/usr/bin/env python3
"""
Demo script để test các chức năng của AI Card Visit Generator
"""

import cv2
import time
from pathlib import Path
from gemini_ocr_service import GeminiOCRService
from ai_generator import AIImageGenerator

def test_camera_detection():
    """Test camera detection"""
    print("🔍 Testing camera detection...")
    
    # Test camera 0 (Logitech)
    cap0 = cv2.VideoCapture(0, cv2.CAP_DSHOW)
    if cap0.isOpened():
        print("✅ Camera 0 (Logitech C270) detected")
        cap0.release()
    else:
        print("❌ Camera 0 not found")
    
    # Test camera 1 (Laptop)
    cap1 = cv2.VideoCapture(1, cv2.CAP_DSHOW)
    if cap1.isOpened():
        print("✅ Camera 1 (Laptop) detected")
        cap1.release()
    else:
        print("❌ Camera 1 not found")

def test_ocr_service():
    """Test OCR service"""
    print("\n📄 Testing OCR service...")
    
    ocr = GeminiOCRService()
    
    # Create a test card image if not exists
    test_card_path = "test_card.jpg"
    if not Path(test_card_path).exists():
        print("⚠️ No test card image found. Please add test_card.jpg")
        return
    
    result = ocr.extract_text_from_card(test_card_path)
    print("OCR Result:")
    for key, value in result.items():
        print(f"  {key}: {value}")

def test_ai_generator():
    """Test AI generator"""
    print("\n🎨 Testing AI generator...")
    
    generator = AIImageGenerator()
    
    # Test with dummy data
    test_face_path = "test_face.jpg"
    if not Path(test_face_path).exists():
        print("⚠️ No test face image found. Please add test_face.jpg")
        return
    
    card_info = {
        'name': 'Nguyễn Văn A',
        'title': 'Software Engineer',
        'company': 'Tech Company',
        'email': '<EMAIL>',
        'phone': '0123456789'
    }
    
    print("Generating AI image (this may take 30-60 seconds)...")
    result = generator.generate_dollhouse_image(test_face_path, card_info)
    
    if result.get('success'):
        print(f"✅ AI generation successful!")
        print(f"   Image path: {result.get('image_path')}")
        print(f"   API used: {result.get('api_used')}")
    else:
        print(f"❌ AI generation failed: {result.get('error')}")

def capture_test_images():
    """Capture test images from cameras"""
    print("\n📸 Capturing test images...")
    
    # Capture from camera 0
    cap0 = cv2.VideoCapture(0, cv2.CAP_DSHOW)
    if cap0.isOpened():
        ret, frame = cap0.read()
        if ret:
            cv2.imwrite("test_card.jpg", frame)
            print("✅ Test card image captured")
        cap0.release()
    
    # Capture from camera 1
    cap1 = cv2.VideoCapture(1, cv2.CAP_DSHOW)
    if cap1.isOpened():
        ret, frame = cap1.read()
        if ret:
            cv2.imwrite("test_face.jpg", frame)
            print("✅ Test face image captured")
        cap1.release()

def main():
    """Main demo function"""
    print("🚀 AI Card Visit Generator - Demo Test")
    print("=" * 50)
    
    # Test 1: Camera detection
    test_camera_detection()
    
    # Test 2: Capture test images
    capture_test_images()
    
    # Test 3: OCR service
    test_ocr_service()
    
    # Test 4: AI generator
    test_ai_generator()
    
    print("\n✅ Demo test completed!")
    print("Now you can run 'python app.py' to start the web application")

if __name__ == "__main__":
    main()
