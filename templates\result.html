<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Card Visit</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .result-section {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .input-images {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .input-images h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .input-item {
            text-align: center;
        }

        .input-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin-bottom: 10px;
        }

        .input-item h4 {
            color: #666;
            font-size: 1em;
        }

        .card-info {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .card-info h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            min-width: 100px;
        }

        .info-value {
            color: #333;
            flex: 1;
            text-align: right;
        }

        .generated-images {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .generated-images h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.6em;
            text-align: center;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .image-item {
            text-align: center;
            position: relative;
        }

        .image-item img {
            width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .image-item img:hover {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .download-btn {
            margin-top: 15px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .action-buttons {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
        }

        .btn {
            padding: 15px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .result-section {
                grid-template-columns: 1fr;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .images-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Kết Quả AI Card Visit</h1>
            <p>Ảnh AI Dollhouse đã được tạo thành công!</p>
        </div>

        <div class="main-content">
            <!-- Input Images and Card Info -->
            <div class="result-section">
                <div class="input-images">
                    <h3>📸 Ảnh Đầu Vào</h3>
                    <div class="input-grid">
                        <div class="input-item">
                            <img src="{{ '/' + card_image if card_image else '/static/placeholder.svg' }}" alt="Name Card">
                            <h4>Name Card</h4>
                        </div>
                        <div class="input-item">
                            <img src="{{ '/' + face_image if face_image else '/static/placeholder.svg' }}" alt="Khuôn Mặt">
                            <h4>Khuôn Mặt</h4>
                        </div>
                    </div>
                </div>

                <div class="card-info">
                    <h3>📄 Thông Tin Trích Xuất</h3>
                    {% if card_info %}
                    <div class="info-item">
                        <span class="info-label">Tên:</span>
                        <span class="info-value">{{ card_info.name or 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Chức vụ:</span>
                        <span class="info-value">{{ card_info.title or 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Công ty:</span>
                        <span class="info-value">{{ card_info.company or 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email:</span>
                        <span class="info-value">{{ card_info.email or 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Điện thoại:</span>
                        <span class="info-value">{{ card_info.phone or 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Website:</span>
                        <span class="info-value">{{ card_info.website or 'N/A' }}</span>
                    </div>
                    {% else %}
                    <p>Không có thông tin card</p>
                    {% endif %}
                </div>
            </div>

            <!-- Generated Images -->
            <div class="generated-images">
                <h3>🎨 Ảnh AI Dollhouse Đã Tạo</h3>
                {% if generated_images %}
                <div class="images-grid">
                    {% for image_path in generated_images %}
                    <div class="image-item">
                        <div class="image-overlay">Phiên bản {{ loop.index }}</div>
                        <img src="{{ '/' + image_path }}" alt="AI Generated Image {{ loop.index }}" onerror="this.src='/static/placeholder.svg'">
                        <button class="download-btn" onclick="downloadImage('{{ image_path }}', 'ai_dollhouse_{{ loop.index }}.png')">
                            💾 Tải về
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p style="text-align: center; color: #666;">Không có ảnh được tạo</p>
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <h3 style="margin-bottom: 20px;">🚀 Tiếp Theo</h3>
                <a href="/" class="btn btn-primary">🔄 Tạo Ảnh Mới</a>
                <button class="btn btn-secondary" onclick="resetSession()">🗑️ Xóa Session</button>
            </div>
        </div>
    </div>

    <script>
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = '/' + imagePath;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function resetSession() {
            if (confirm('Bạn có chắc muốn xóa session hiện tại?')) {
                fetch('/reset_session', {
                    method: 'POST'
                })
                .then(res => res.json())
                .then(data => {
                    if (data.status === 'success') {
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    alert('Lỗi reset session: ' + error);
                });
            }
        }
    </script>
</body>
</html>
