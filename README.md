# 🎨 AI Card Visit Generator

Ứng dụng tạo ảnh AI Dollhouse từ name card và khuôn mặt sử dụng camera streaming trực tiếp.

## ✨ Tính năng chính

### 📷 Camera Streaming
- **Camera 1**: Logitech C270 (USB) - Chụp name card
- **Camera 2**: Camera laptop - Chụ<PERSON> khuôn mặt
- Streaming trực tiếp với FPS cao (30-60 FPS)
- Điều chỉnh focus cho camera Logitech
- Giữ ảnh đã chụp để xem trước

### 🔍 OCR (Optical Character Recognition)
- Sử dụng Gemini 2.5 Flash cho OCR chính xác cao
- Trích xuất thông tin từ name card:
  - <PERSON><PERSON><PERSON>, chứ<PERSON> v<PERSON>, công ty
  - <PERSON><PERSON>, đi<PERSON><PERSON> thoại, website
  - Địa chỉ và thông tin bổ sung
- Hỗ trợ tiếng Việt với dấu

### 🎨 AI Image Generation
- Sử dụng Gemini 2.0 Flash Preview Image Generation
- Tạo ảnh Dollhouse miniature chuyên nghiệp
- <PERSON><PERSON><PERSON> hợ<PERSON> khuôn mặt thật với thông tin name card
- T<PERSON>o 2 phiên bản khác nhau
- <PERSON><PERSON><PERSON> l<PERSON> cao, phù hợp in ấn

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Python 3.8+
- Camera Logitech C270 (kết nối USB)
- Camera laptop tích hợp
- Windows/Linux/macOS

### Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### Cấu hình API Key
Chỉnh sửa file `.env`:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### Chạy ứng dụng
```bash
python app.py
```

Truy cập: http://127.0.0.1:5000

## 📱 Hướng dẫn sử dụng

### Bước 1: Chụp ảnh
1. **Chụp Name Card**: Đặt name card vào khung camera trái, nhấn "📸 Chụp Name Card"
2. **Chụp Khuôn Mặt**: Nhìn thẳng vào camera phải, nhấn "📸 Chụp Khuôn Mặt"
3. Có thể "🔄 Chụp lại" nếu không hài lòng

### Bước 2: Điều chỉnh (tùy chọn)
- Sử dụng thanh trượt "🔍 Điều chỉnh nét" cho camera Logitech
- Kiểm tra ảnh đã chụp trước khi xử lý

### Bước 3: Tạo AI
1. Nhấn "✨ Tạo Ảnh AI Dollhouse"
2. Chờ quá trình OCR và AI generation (30-60 giây)
3. Xem kết quả trên màn hình mới

### Bước 4: Tải về
- Tải về các phiên bản ảnh AI đã tạo
- Chọn "🔄 Tạo Ảnh Mới" để bắt đầu lại

## 🎯 Kết quả mong đợi

Ứng dụng sẽ tạo ra ảnh Dollhouse miniature với:
- Figurine có khuôn mặt giống người thật
- Môi trường văn phòng phù hợp với chức vụ
- Nameplate và biển hiệu công ty
- Chất lượng cao, phong cách chuyên nghiệp
- Kích thước business card chuẩn (9x5.5cm)

## 🔧 Cấu trúc dự án

```
Ai_Gen/
├── app.py                 # Flask application chính
├── gemini_ocr_service.py  # Service OCR
├── ai_generator.py        # Service tạo ảnh AI
├── templates/
│   ├── index.html         # Màn hình chụp ảnh
│   └── result.html        # Màn hình kết quả
├── static/
│   ├── img/              # Ảnh đã chụp
│   ├── css/              # CSS files
│   └── js/               # JavaScript files
├── outputs/              # Ảnh AI đã tạo
├── .env                  # Cấu hình API keys
└── README.md            # Hướng dẫn này
```

## 🛠️ Troubleshooting

### Camera không hoạt động
- Kiểm tra kết nối USB của Logitech C270
- Đảm bảo camera laptop không bị ứng dụng khác sử dụng
- Restart ứng dụng nếu cần

### OCR không chính xác
- Đảm bảo name card rõ nét, đủ sáng
- Đặt name card thẳng trong khung hình
- Thử chụp lại với góc độ khác

### AI generation lỗi
- Kiểm tra kết nối internet
- Xác nhận API key Gemini hợp lệ
- Thử lại sau vài phút

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra console log trong trình duyệt
2. Xem terminal output của Python
3. Đảm bảo tất cả dependencies đã được cài đặt

---

**Phát triển bởi**: AI Card Visit Team  
**Phiên bản**: 1.0.0  
**Cập nhật**: 2024-12-30
