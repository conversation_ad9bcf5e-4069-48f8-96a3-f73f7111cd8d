<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Streaming</title>
    <style>
        #camera1, #camera2 {
            width: 45%;
            margin: 2%;
            border: 1px solid #000;
        }
        button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h2>Camera Streaming</h2>
    <div>
        <img id="camera1" src="/video_feed/0" width="640" height="480">
        <img id="camera2" src="/video_feed/1" width="640" height="480">

    </div>
    <button onclick="captureImages()">Chụp ảnh</button>

    <script>
        let camera1Stream, camera2Stream;

        // Hàm để lấy và khởi tạo các camera
        async function startCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices(); // L<PERSON><PERSON> tất cả thiết bị video

                let camera1, camera2;

                // Tìm camera Logitech C270 (USB)
                camera1 = devices.find(device => device.kind === 'videoinput' && device.label.includes('Logitech C270'));
                // Tìm camera Laptop
                camera2 = devices.find(device => device.kind === 'videoinput' && device.label.includes('HD Camera'));

                if (camera1 && camera2) {
                    // Khởi tạo camera Logitech C270 (deviceId từ camera1)
                    const camera1Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera1.deviceId }
                    });
                    document.getElementById('camera1').srcObject = camera1Stream;
                    camera1Stream = camera1Stream;

                    // Khởi tạo camera Laptop (deviceId từ camera2)
                    const camera2Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera2.deviceId }
                    });
                    document.getElementById('camera2').srcObject = camera2Stream;
                    camera2Stream = camera2Stream;
                } else {
                    alert('Không tìm thấy các camera phù hợp.');
                }
            } catch (error) {
                console.error('Error accessing cameras:', error);
            }
        }

        // Chụp ảnh từ hai camera và gửi lên backend
        async function captureImages() {
            const canvas1 = document.createElement('canvas');
            const canvas2 = document.createElement('canvas');
            canvas1.getContext('2d').drawImage(document.getElementById('camera1'), 0, 0);
            canvas2.getContext('2d').drawImage(document.getElementById('camera2'), 0, 0);

            const img1 = canvas1.toDataURL('image/png');
            const img2 = canvas2.toDataURL('image/png');

            // Gửi ảnh tới backend Flask để xử lý
            sendToBackend(img1, img2);
        }

        // Gửi ảnh đến backend Flask
        function sendToBackend(img1, img2) {
            fetch('/process_images', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ img1, img2 })
            })
            .then(response => response.json())
            .then(data => {
                console.log("Kết quả xử lý", data);
                // Hiển thị ảnh kết quả sau khi xử lý (nếu cần)
            })
            .catch(error => console.error('Error:', error));
        }

        // Bắt đầu streaming các camera khi load trang
        startCameras(); 
    </script>
</body>
</html> -->

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .camera-section {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 40px;
        }

        .camera-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .camera-container:hover {
            transform: translateY(-5px);
        }

        .camera-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .camera-title.card {
            color: #e74c3c;
        }

        .camera-title.face {
            color: #3498db;
        }

        .camera-frame {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .camera-frame img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
        }

        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2em;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .camera-frame:hover .camera-overlay {
            opacity: 1;
        }

        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-success {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .generate-section {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            margin-top: 30px;
        }

        .generate-btn {
            font-size: 1.3em;
            padding: 15px 40px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
        }

        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #95a5a6; }
        .status-captured { background: #27ae60; }
        .status-processing { background: #f39c12; animation: pulse 1.5s infinite; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .camera-section {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 AI Card Visit Generator</h1>
            <p>Chụp ảnh name card và khuôn mặt để tạo ảnh AI độc đáo</p>
        </div>

        <div class="main-content">
            <div class="camera-section">
                <!-- Camera 0: Name Card -->
                <div class="camera-container">
                    <div class="camera-title card">
                        <span class="status-indicator" id="status0"></span>
                        📄 Camera Name Card (Logitech C270)
                    </div>
                    <div class="camera-frame">
                        <img id="cam0" src="/video_feed/0" alt="Camera Name Card">
                        <div class="camera-overlay">
                            <span>Đặt name card vào khung hình</span>
                        </div>
                    </div>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="captureImage(0)">
                            📸 Chụp Name Card
                        </button>
                        <button class="btn btn-secondary" onclick="restoreStream(0)" style="display:none;" id="restore0">
                            🔄 Chụp lại
                        </button>
                    </div>

                    <!-- Focus Control for Logitech Camera -->
                    <div style="margin-top: 15px; text-align: center;">
                        <label for="focusSlider" style="display: block; margin-bottom: 5px; color: #666;">🔍 Điều chỉnh nét:</label>
                        <input type="range" id="focusSlider" min="0" max="255" value="0"
                               style="width: 80%; margin-bottom: 5px;"
                               oninput="adjustFocus(this.value)">
                        <div style="font-size: 0.9em; color: #888;">Giá trị: <span id="focusValue">0</span></div>
                    </div>
                </div>

                <!-- Camera 1: Face -->
                <div class="camera-container">
                    <div class="camera-title face">
                        <span class="status-indicator" id="status1"></span>
                        👤 Camera Khuôn Mặt (Laptop)
                    </div>
                    <div class="camera-frame">
                        <img id="cam1" src="/video_feed/1" alt="Camera Khuôn Mặt">
                        <div class="camera-overlay">
                            <span>Nhìn thẳng vào camera</span>
                        </div>
                    </div>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="captureImage(1)">
                            📸 Chụp Khuôn Mặt
                        </button>
                        <button class="btn btn-secondary" onclick="restoreStream(1)" style="display:none;" id="restore1">
                            🔄 Chụp lại
                        </button>
                    </div>
                </div>
            </div>

            <!-- Generate Section -->
            <div class="generate-section">
                <h3>🚀 Tạo Ảnh AI Dollhouse</h3>
                <p style="margin: 15px 0; color: #666;">Sau khi chụp cả 2 ảnh, nhấn nút bên dưới để tạo ảnh AI</p>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <button class="generate-btn" id="generateBtn" onclick="generateAI()" disabled>
                    ✨ Tạo Ảnh AI Dollhouse
                </button>

                <div id="statusMessage" style="margin-top: 20px; font-weight: bold;"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let capturedImages = {
            card: false,
            face: false
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatusIndicators();
            checkSessionStatus();
        });

        function captureImage(cameraId) {
            const cameraName = cameraId === 0 ? 'card' : 'face';
            const statusId = `status${cameraId}`;

            // Update status to processing
            document.getElementById(statusId).className = 'status-indicator status-processing';

            fetch(`/capture_step/${cameraId}`, {
                method: 'POST'
            })
            .then(res => res.json())
            .then(data => {
                if (data.status === 'success') {
                    const imgTag = document.getElementById(`cam${cameraId}`);
                    const restoreBtn = document.getElementById(`restore${cameraId}`);

                    // Show captured image
                    imgTag.src = '/' + data.image_path + '?t=' + new Date().getTime();
                    restoreBtn.style.display = 'inline-flex';

                    // Update status
                    capturedImages[cameraName] = true;
                    document.getElementById(statusId).className = 'status-indicator status-captured';

                    // Update generate button
                    updateGenerateButton();

                    // Show success message
                    showMessage(`✅ Đã chụp ${cameraName === 'card' ? 'name card' : 'khuôn mặt'} thành công!`, 'success');
                } else {
                    document.getElementById(statusId).className = 'status-indicator status-ready';
                    showMessage(`❌ Lỗi: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                document.getElementById(statusId).className = 'status-indicator status-ready';
                showMessage('❌ Lỗi kết nối: ' + error, 'error');
            });
        }

        function restoreStream(cameraId) {
            const cameraName = cameraId === 0 ? 'card' : 'face';
            const imgTag = document.getElementById(`cam${cameraId}`);
            const restoreBtn = document.getElementById(`restore${cameraId}`);
            const statusId = `status${cameraId}`;

            // Restore stream
            imgTag.src = `/video_feed/${cameraId}?t=` + new Date().getTime();
            restoreBtn.style.display = 'none';

            // Update status
            capturedImages[cameraName] = false;
            document.getElementById(statusId).className = 'status-indicator status-ready';

            // Update generate button
            updateGenerateButton();
        }

        function updateGenerateButton() {
            const generateBtn = document.getElementById('generateBtn');
            const canGenerate = capturedImages.card && capturedImages.face;

            generateBtn.disabled = !canGenerate;

            if (canGenerate) {
                generateBtn.innerHTML = '✨ Tạo Ảnh AI Dollhouse';
                generateBtn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            } else {
                generateBtn.innerHTML = '⏳ Vui lòng chụp cả 2 ảnh';
                generateBtn.style.background = '#95a5a6';
            }
        }

        function generateAI() {
            if (!capturedImages.card || !capturedImages.face) {
                showMessage('❌ Vui lòng chụp cả 2 ảnh trước khi tạo AI', 'error');
                return;
            }

            const generateBtn = document.getElementById('generateBtn');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            // Show processing state
            generateBtn.disabled = true;
            generateBtn.innerHTML = '🔄 Đang xử lý...';
            progressBar.style.display = 'block';

            // Animate progress bar
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressFill.style.width = progress + '%';
            }, 500);

            showMessage('🔍 Đang OCR name card...', 'processing');

            fetch('/process_images', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(res => res.json())
            .then(data => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';

                if (data.status === 'success') {
                    showMessage('✅ Xử lý thành công! Chuyển đến trang kết quả...', 'success');

                    setTimeout(() => {
                        window.location.href = '/result';
                    }, 2000);
                } else {
                    showMessage(`❌ ${data.message}`, 'error');
                    resetGenerateButton();
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                showMessage('❌ Lỗi xử lý: ' + error, 'error');
                resetGenerateButton();
            });
        }

        function resetGenerateButton() {
            const generateBtn = document.getElementById('generateBtn');
            const progressBar = document.getElementById('progressBar');

            generateBtn.disabled = false;
            generateBtn.innerHTML = '✨ Tạo Ảnh AI Dollhouse';
            generateBtn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            progressBar.style.display = 'none';
        }

        function showMessage(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = message;

            // Set color based on type
            switch(type) {
                case 'success':
                    statusMessage.style.color = '#27ae60';
                    break;
                case 'error':
                    statusMessage.style.color = '#e74c3c';
                    break;
                case 'processing':
                    statusMessage.style.color = '#f39c12';
                    break;
                default:
                    statusMessage.style.color = '#333';
            }
        }

        function updateStatusIndicators() {
            document.getElementById('status0').className = 'status-indicator status-ready';
            document.getElementById('status1').className = 'status-indicator status-ready';
        }

        function checkSessionStatus() {
            fetch('/get_session_status')
            .then(res => res.json())
            .then(data => {
                if (data.has_card) {
                    capturedImages.card = true;
                    document.getElementById('status0').className = 'status-indicator status-captured';
                    document.getElementById('restore0').style.display = 'inline-flex';
                }
                if (data.has_face) {
                    capturedImages.face = true;
                    document.getElementById('status1').className = 'status-indicator status-captured';
                    document.getElementById('restore1').style.display = 'inline-flex';
                }
                updateGenerateButton();
            })
            .catch(error => {
                console.log('Session check failed:', error);
            });
        }

        // Auto-refresh session status every 5 seconds
        setInterval(checkSessionStatus, 5000);

        // Focus adjustment for Logitech camera
        function adjustFocus(value) {
            document.getElementById('focusValue').textContent = value;

            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    focus_value: parseInt(value)
                })
            })
            .then(res => res.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('Focus adjusted to:', value);
                } else {
                    console.error('Focus adjustment failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Focus adjustment error:', error);
            });
        }
    </script>
</body>
</html>

