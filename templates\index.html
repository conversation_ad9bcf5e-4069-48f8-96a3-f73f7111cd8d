<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Streaming</title>
    <style>
        #camera1, #camera2 {
            width: 45%;
            margin: 2%;
            border: 1px solid #000;
        }
        button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h2>Camera Streaming</h2>
    <div>
        <img id="camera1" src="/video_feed/0" width="640" height="480">
        <img id="camera2" src="/video_feed/1" width="640" height="480">

    </div>
    <button onclick="captureImages()">Chụp ảnh</button>

    <script>
        let camera1Stream, camera2Stream;

        // Hàm để lấy và khởi tạo các camera
        async function startCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices(); // L<PERSON><PERSON> tất cả thiết bị video

                let camera1, camera2;

                // Tìm camera Logitech C270 (USB)
                camera1 = devices.find(device => device.kind === 'videoinput' && device.label.includes('Logitech C270'));
                // Tìm camera Laptop
                camera2 = devices.find(device => device.kind === 'videoinput' && device.label.includes('HD Camera'));

                if (camera1 && camera2) {
                    // Khởi tạo camera Logitech C270 (deviceId từ camera1)
                    const camera1Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera1.deviceId }
                    });
                    document.getElementById('camera1').srcObject = camera1Stream;
                    camera1Stream = camera1Stream;

                    // Khởi tạo camera Laptop (deviceId từ camera2)
                    const camera2Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera2.deviceId }
                    });
                    document.getElementById('camera2').srcObject = camera2Stream;
                    camera2Stream = camera2Stream;
                } else {
                    alert('Không tìm thấy các camera phù hợp.');
                }
            } catch (error) {
                console.error('Error accessing cameras:', error);
            }
        }

        // Chụp ảnh từ hai camera và gửi lên backend
        async function captureImages() {
            const canvas1 = document.createElement('canvas');
            const canvas2 = document.createElement('canvas');
            canvas1.getContext('2d').drawImage(document.getElementById('camera1'), 0, 0);
            canvas2.getContext('2d').drawImage(document.getElementById('camera2'), 0, 0);

            const img1 = canvas1.toDataURL('image/png');
            const img2 = canvas2.toDataURL('image/png');

            // Gửi ảnh tới backend Flask để xử lý
            sendToBackend(img1, img2);
        }

        // Gửi ảnh đến backend Flask
        function sendToBackend(img1, img2) {
            fetch('/process_images', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ img1, img2 })
            })
            .then(response => response.json())
            .then(data => {
                console.log("Kết quả xử lý", data);
                // Hiển thị ảnh kết quả sau khi xử lý (nếu cần)
            })
            .catch(error => console.error('Error:', error));
        }

        // Bắt đầu streaming các camera khi load trang
        startCameras(); 
    </script>
</body>
</html> -->

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .camera-section {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 40px;
        }

        .camera-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .camera-container:hover {
            transform: translateY(-5px);
        }

        .camera-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .camera-title.card {
            color: #e74c3c;
        }

        .camera-title.face {
            color: #3498db;
        }

        .camera-frame {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .camera-frame img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
        }

        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2em;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .camera-frame:hover .camera-overlay {
            opacity: 1;
        }

        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-success {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .generate-section {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            margin-top: 30px;
        }

        .generate-btn {
            font-size: 1.3em;
            padding: 15px 40px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
        }

        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #95a5a6; }
        .status-captured { background: #27ae60; }
        .status-processing { background: #f39c12; animation: pulse 1.5s infinite; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* AI Configuration Modal Styles */
        .ai-config-section {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .config-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 3px solid #667eea;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        .config-card h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
            text-align: center;
        }

        .config-group {
            margin-bottom: 20px;
        }

        .config-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .config-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .config-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .config-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 0.9em;
            font-family: 'Courier New', monospace;
            background: white;
            resize: vertical;
            min-height: 120px;
            transition: border-color 0.3s ease;
        }

        .config-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @media (max-width: 768px) {
            .camera-section {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 AI Card Visit Generator</h1>
            <p>Chụp ảnh name card và khuôn mặt để tạo ảnh AI độc đáo</p>
        </div>

        <div class="main-content">
            <div class="camera-section">
                <!-- Camera 0: Name Card -->
                <div class="camera-container">
                    <div class="camera-title card">
                        <span class="status-indicator" id="status0"></span>
                        📄 Camera Name Card (Logitech C270)
                    </div>
                    <div class="camera-frame">
                        <img id="cam0" src="/video_feed/0" alt="Camera Name Card">
                        <div class="camera-overlay">
                            <span>Đặt name card vào khung hình</span>
                        </div>
                    </div>
                    <!-- Focus Control for Logitech Camera -->
                    <div style="margin-top: 15px; text-align: center;">
                        <label for="focusSlider" style="display: block; margin-bottom: 5px; color: #666;">🔍 Điều chỉnh nét:</label>
                        <input type="range" id="focusSlider" min="0" max="255" value="0"
                               style="width: 80%; margin-bottom: 5px;"
                               oninput="adjustFocus(this.value)">
                        <div style="font-size: 0.9em; color: #888;">Giá trị: <span id="focusValue">0</span></div>
                    </div>
                </div>

                <!-- Camera 1: Face -->
                <div class="camera-container">
                    <div class="camera-title face">
                        <span class="status-indicator" id="status1"></span>
                        👤 Camera Khuôn Mặt (Laptop)
                    </div>
                    <div class="camera-frame">
                        <img id="cam1" src="/video_feed/1" alt="Camera Khuôn Mặt">
                        <div class="camera-overlay">
                            <span>Nhìn thẳng vào camera</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Single Capture Button -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn btn-primary" id="singleCaptureBtn" onclick="singleCapture()" style="font-size: 1.2em; padding: 15px 30px;">
                    📸 Chụp Name Card
                </button>
                <div id="captureInstructions" style="margin-top: 10px; color: #666; font-size: 0.9em;">
                    Bước 1: Chụp name card trước
                </div>
                <button class="btn btn-secondary" id="retakeBtn" onclick="retakeAll()" style="display: none; margin-top: 10px;">
                    🔄 Chụp lại từ đầu
                </button>
            </div>
            </div>

            <!-- Generate Section -->
            <div class="generate-section">
                <h3>🚀 Tạo Ảnh AI Dollhouse</h3>
                <p style="margin: 15px 0; color: #666;">Sau khi chụp cả 2 ảnh, nhấn nút bên dưới để tạo ảnh AI</p>

                <button class="generate-btn" id="generateBtn" onclick="showAIConfig()" disabled>
                    ✨ Tạo Ảnh AI Dollhouse
                </button>

                <!-- AI Model Selection (Hidden initially) -->
                <div class="ai-config-section" id="aiConfigSection" style="display: none;">
                    <div class="config-card">
                        <h3>🤖 Cấu hình AI Generation</h3>

                        <!-- Model Selection -->
                        <div class="config-group">
                            <label for="modelSelect">Chọn mô hình AI:</label>
                            <select id="modelSelect" class="config-select">
                                <option value="gemini">🔥 Gemini 2.0 Flash (Recommended)</option>
                                <option value="chatgpt">💬 ChatGPT DALL-E 3</option>
                                <option value="huggingface">🤗 Hugging Face Diffusion</option>
                                <option value="pollinations">🌸 Pollinations AI</option>
                                <option value="stable-diffusion">🎨 Stable Diffusion</option>
                            </select>
                        </div>

                        <!-- Prompt Selection -->
                        <div class="config-group">
                            <label for="promptSelect">Chọn prompt template:</label>
                            <select id="promptSelect" class="config-select" onchange="updatePromptPreview()">
                                <option value="dollhouse">🏠 Professional Dollhouse Scene</option>
                                <option value="miniature">🎭 Miniature Figure Collection</option>
                                <option value="office">💼 Professional Office Environment</option>
                                <option value="creative">🎨 Creative Artistic Style</option>
                            </select>
                        </div>

                        <!-- Edit Prompt -->
                        <div class="config-group">
                            <label for="promptEdit">Edit prompt (sẽ được gửi đến AI model):</label>
                            <textarea id="promptEdit" class="config-textarea" rows="6" placeholder="Nhập prompt tùy chỉnh hoặc chọn template ở trên..."></textarea>
                            <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                                💡 Bạn có thể chỉnh sửa trực tiếp prompt này. AI model sẽ sử dụng prompt này để tạo ảnh.
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="config-group" style="text-align: center; margin-top: 25px;">
                            <button class="btn btn-primary" onclick="generateAI()" style="margin-right: 10px;">
                                🚀 Bắt đầu tạo ảnh
                            </button>
                            <button class="btn btn-secondary" onclick="hideAIConfig()">
                                ❌ Hủy
                            </button>
                        </div>
                    </div>
                </div>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div id="statusMessage" style="margin-top: 20px; font-weight: bold;"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let capturedImages = {
            card: false,
            face: false
        };
        let captureStep = 0; // 0: chưa chụp gì, 1: đã chụp card, 2: đã chụp cả 2

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatusIndicators();
            checkSessionStatus();
            initializePromptTemplates();
        });

        function singleCapture() {
            const btn = document.getElementById('singleCaptureBtn');
            const instructions = document.getElementById('captureInstructions');

            if (captureStep === 0) {
                // Chụp name card
                btn.innerHTML = '⏳ Đang chụp name card...';
                btn.disabled = true;
                instructions.innerHTML = 'Đang chụp name card...';

                captureImage(0, () => {
                    // Success callback
                    captureStep = 1;
                    btn.innerHTML = '👤 Chụp khuôn mặt';
                    btn.disabled = false;
                    instructions.innerHTML = 'Bước 2: Chụp khuôn mặt';
                });
            } else if (captureStep === 1) {
                // Chụp face
                btn.innerHTML = '⏳ Đang chụp khuôn mặt...';
                btn.disabled = true;
                instructions.innerHTML = 'Đang chụp khuôn mặt...';

                captureImage(1, () => {
                    // Success callback
                    captureStep = 2;
                    btn.style.display = 'none';
                    instructions.innerHTML = '✅ Đã chụp xong cả 2 ảnh!';
                    document.getElementById('retakeBtn').style.display = 'inline-block';
                    updateGenerateButton();
                });
            }
        }

        function retakeAll() {
            // Reset tất cả
            captureStep = 0;
            capturedImages.card = false;
            capturedImages.face = false;

            // Reset UI
            const btn = document.getElementById('singleCaptureBtn');
            const instructions = document.getElementById('captureInstructions');
            const retakeBtn = document.getElementById('retakeBtn');

            btn.innerHTML = '📸 Chụp Name Card';
            btn.disabled = false;
            btn.style.display = 'inline-block';
            instructions.innerHTML = 'Bước 1: Chụp name card trước';
            retakeBtn.style.display = 'none';

            // Restore camera streams
            restoreStream(0);
            restoreStream(1);

            // Update status indicators
            document.getElementById('status0').className = 'status-indicator status-ready';
            document.getElementById('status1').className = 'status-indicator status-ready';

            updateGenerateButton();
        }

        function captureImage(cameraId, successCallback) {
            const cameraName = cameraId === 0 ? 'card' : 'face';
            const statusId = `status${cameraId}`;

            // Update status to processing
            document.getElementById(statusId).className = 'status-indicator status-processing';

            fetch(`/capture_step/${cameraId}`, {
                method: 'POST'
            })
            .then(res => res.json())
            .then(data => {
                if (data.status === 'success') {
                    const imgTag = document.getElementById(`cam${cameraId}`);

                    // Show captured image
                    imgTag.src = '/' + data.image_path + '?t=' + new Date().getTime();

                    // Update status
                    document.getElementById(statusId).className = 'status-indicator status-captured';
                    capturedImages[cameraName] = true;

                    showMessage(`✅ Đã chụp ${cameraName === 'card' ? 'name card' : 'khuôn mặt'} thành công!`, 'success');

                    // Call success callback if provided
                    if (successCallback) {
                        successCallback();
                    }
                } else {
                    document.getElementById(statusId).className = 'status-indicator status-ready';
                    showMessage(`❌ Lỗi: ${data.message}`, 'error');

                    // Reset capture step on error
                    if (cameraId === 0) {
                        captureStep = 0;
                        document.getElementById('singleCaptureBtn').innerHTML = '📸 Chụp Name Card';
                        document.getElementById('singleCaptureBtn').disabled = false;
                        document.getElementById('captureInstructions').innerHTML = 'Bước 1: Chụp name card trước';
                    } else {
                        captureStep = 1;
                        document.getElementById('singleCaptureBtn').innerHTML = '👤 Chụp khuôn mặt';
                        document.getElementById('singleCaptureBtn').disabled = false;
                        document.getElementById('captureInstructions').innerHTML = 'Bước 2: Chụp khuôn mặt';
                    }
                }
            })
            .catch(error => {
                document.getElementById(statusId).className = 'status-indicator status-ready';
                showMessage('❌ Lỗi kết nối: ' + error, 'error');

                // Reset on error
                if (cameraId === 0) {
                    captureStep = 0;
                    document.getElementById('singleCaptureBtn').innerHTML = '📸 Chụp Name Card';
                } else {
                    captureStep = 1;
                    document.getElementById('singleCaptureBtn').innerHTML = '👤 Chụp khuôn mặt';
                }
                document.getElementById('singleCaptureBtn').disabled = false;
            });
        }

        function restoreStream(cameraId) {
            const cameraName = cameraId === 0 ? 'card' : 'face';
            const imgTag = document.getElementById(`cam${cameraId}`);
            const btn = document.getElementById(`captureBtn${cameraId}`);
            const statusId = `status${cameraId}`;

            // Restore stream
            imgTag.src = `/video_feed/${cameraId}?t=` + new Date().getTime();

            // Update button back to "Chụp"
            btn.innerHTML = cameraId === 0 ? '📸 Chụp Name Card' : '👤 Chụp Khuôn Mặt';

            // Update status
            capturedImages[cameraName] = false;
            document.getElementById(statusId).className = 'status-indicator status-ready';

            // Update generate button
            updateGenerateButton();

            showMessage(`🔄 Đã khôi phục camera ${cameraName === 'card' ? 'name card' : 'khuôn mặt'}`, 'info');
        }

        function updateGenerateButton() {
            const generateBtn = document.getElementById('generateBtn');
            const canGenerate = capturedImages.card && capturedImages.face;

            generateBtn.disabled = !canGenerate;

            if (canGenerate) {
                generateBtn.innerHTML = '✨ Tạo Ảnh AI Dollhouse';
                generateBtn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            } else {
                generateBtn.innerHTML = '⏳ Vui lòng chụp cả 2 ảnh';
                generateBtn.style.background = '#95a5a6';
            }
        }

        function showAIConfig() {
            if (!capturedImages.card || !capturedImages.face) {
                showMessage('❌ Vui lòng chụp cả 2 ảnh trước khi tạo AI', 'error');
                return;
            }

            const aiConfigSection = document.getElementById('aiConfigSection');
            const generateBtn = document.getElementById('generateBtn');

            // Hide generate button and show config
            generateBtn.style.display = 'none';
            aiConfigSection.style.display = 'block';

            // Initialize prompt preview
            updatePromptPreview();
        }

        function hideAIConfig() {
            const aiConfigSection = document.getElementById('aiConfigSection');
            const generateBtn = document.getElementById('generateBtn');

            // Show generate button and hide config
            generateBtn.style.display = 'block';
            aiConfigSection.style.display = 'none';
        }

        function generateAI() {
            if (!capturedImages.card || !capturedImages.face) {
                showMessage('❌ Vui lòng chụp cả 2 ảnh trước khi tạo AI', 'error');
                return;
            }

            // Get selected model and edited prompt
            const selectedModel = document.getElementById('modelSelect').value;
            const selectedTemplate = document.getElementById('promptSelect').value;
            const editedPrompt = document.getElementById('promptEdit').value;

            const aiConfigSection = document.getElementById('aiConfigSection');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            // Hide config and show progress
            aiConfigSection.style.display = 'none';
            progressBar.style.display = 'block';

            // Animate progress bar
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressFill.style.width = progress + '%';
            }, 500);

            showMessage(`🔍 Đang tạo ảnh với ${selectedModel.toUpperCase()}...`, 'processing');

            // Send request with AI config
            fetch('/process_images', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: selectedModel,
                    prompt_template: selectedTemplate,
                    custom_prompt: editedPrompt
                })
            })
            .then(res => res.json())
            .then(data => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';

                if (data.status === 'success') {
                    showMessage(`✅ Tạo ảnh thành công với ${selectedModel.toUpperCase()}! Chuyển đến trang kết quả...`, 'success');

                    setTimeout(() => {
                        window.location.href = '/result';
                    }, 2000);
                } else {
                    showMessage(`❌ ${data.message}`, 'error');
                    resetGenerateButton();
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                showMessage('❌ Lỗi xử lý: ' + error, 'error');
                resetGenerateButton();
            });
        }

        function resetGenerateButton() {
            const generateBtn = document.getElementById('generateBtn');
            const progressBar = document.getElementById('progressBar');
            const aiConfigSection = document.getElementById('aiConfigSection');

            generateBtn.disabled = false;
            generateBtn.innerHTML = '✨ Tạo Ảnh AI Dollhouse';
            generateBtn.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            generateBtn.style.display = 'block';
            progressBar.style.display = 'none';
            aiConfigSection.style.display = 'none';
        }

        function showMessage(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = message;

            // Set color based on type
            switch(type) {
                case 'success':
                    statusMessage.style.color = '#27ae60';
                    break;
                case 'error':
                    statusMessage.style.color = '#e74c3c';
                    break;
                case 'processing':
                    statusMessage.style.color = '#f39c12';
                    break;
                default:
                    statusMessage.style.color = '#333';
            }
        }

        function updateStatusIndicators() {
            document.getElementById('status0').className = 'status-indicator status-ready';
            document.getElementById('status1').className = 'status-indicator status-ready';
        }

        function checkSessionStatus() {
            fetch('/get_session_status')
            .then(res => res.json())
            .then(data => {
                if (data.has_card) {
                    capturedImages.card = true;
                    document.getElementById('status0').className = 'status-indicator status-captured';
                    document.getElementById('restore0').style.display = 'inline-flex';
                }
                if (data.has_face) {
                    capturedImages.face = true;
                    document.getElementById('status1').className = 'status-indicator status-captured';
                    document.getElementById('restore1').style.display = 'inline-flex';
                }
                updateGenerateButton();
            })
            .catch(error => {
                console.log('Session check failed:', error);
            });
        }

        // Auto-refresh session status every 5 seconds
        setInterval(checkSessionStatus, 5000);

        // Focus adjustment for Logitech camera
        function adjustFocus(value) {
            document.getElementById('focusValue').textContent = value;

            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    focus_value: parseInt(value)
                })
            })
            .then(res => res.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('Focus adjusted to:', value);
                } else {
                    console.error('Focus adjustment failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Focus adjustment error:', error);
            });
        }

        // Prompt templates
        const promptTemplates = {
            dollhouse: `Create a highly detailed dollhouse miniature scene showing a professional workspace environment.

SCENE REQUIREMENTS:
- Top-down perspective showing a complete dollhouse room
- Professional office/workspace setting appropriate for the person's job title
- Miniature scale furniture and accessories
- Warm, cozy lighting typical of dollhouse photography
- Handcrafted, collectible dollhouse aesthetic

ENVIRONMENT DETAILS:
- Include miniature desk, chair, and profession-specific items
- Add small nameplate with person's name
- Professional color scheme and materials
- Realistic miniature textures and details

STYLE SPECIFICATIONS:
- High-quality dollhouse miniature photography
- Detailed craftsmanship and realistic textures
- Warm ambient lighting
- Sharp focus on all miniature details
- Professional product photography quality

The scene should look like an expensive, handcrafted dollhouse room that a collector would display.`,

            miniature: `Create a collectible miniature figure scene with professional theme.

REQUIREMENTS:
- Miniature figure collection display style
- Professional workspace diorama
- High-end collectible quality
- Museum display lighting
- Detailed miniature accessories and furniture
- Person's profession reflected in the scene setup`,

            office: `Create a professional office environment scene.

REQUIREMENTS:
- Modern professional office setting
- Clean, corporate aesthetic
- Professional lighting
- Business-appropriate furniture and decor
- Reflects the person's professional role
- High-quality architectural visualization style`,

            creative: `Create an artistic, creative interpretation of a professional workspace.

REQUIREMENTS:
- Artistic, stylized approach
- Creative lighting and composition
- Unique perspective and visual style
- Professional theme with artistic flair
- Gallery-worthy presentation
- Creative use of colors and textures`
        };

        function initializePromptTemplates() {
            updatePromptPreview();
        }

        function updatePromptPreview() {
            const promptSelect = document.getElementById('promptSelect');
            const promptEdit = document.getElementById('promptEdit');
            const selectedTemplate = promptSelect.value;

            // Load template into editable textarea
            promptEdit.value = promptTemplates[selectedTemplate] || '';
        }
    </script>
</body>
</html>

