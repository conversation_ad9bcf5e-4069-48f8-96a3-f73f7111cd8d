#!/usr/bin/env python3
"""
Test script để kiểm tra chức năng tạo ảnh AI
"""

import requests
import json
import time
from pathlib import Path

def test_ai_generation():
    """Test AI generation functionality"""
    print("🧪 Testing AI Generation...")
    
    # Test data
    test_card_info = {
        'name': 'Nguyễn Văn Test',
        'title': 'Software Engineer',
        'company': 'Tech Company Vietnam',
        'email': '<EMAIL>',
        'phone': '0123456789',
        'website': 'www.techcompany.vn'
    }
    
    # Test with simple AI generator
    from ai_generator_simple import AIImageGenerator
    
    generator = AIImageGenerator()
    
    # Create a dummy face image if not exists
    face_path = "static/img/test_face.jpg"
    if not Path(face_path).exists():
        print("⚠️ Creating dummy face image...")
        from PIL import Image, ImageDraw
        
        # Create a simple face image
        img = Image.new('RGB', (400, 400), (255, 220, 177))  # Skin color
        draw = ImageDraw.Draw(img)
        
        # Draw simple face features
        # Eyes
        draw.ellipse([120, 150, 140, 170], fill=(0, 0, 0))  # Left eye
        draw.ellipse([260, 150, 280, 170], fill=(0, 0, 0))  # Right eye
        
        # Nose
        draw.ellipse([190, 180, 210, 200], fill=(255, 200, 150))
        
        # Mouth
        draw.arc([170, 220, 230, 250], 0, 180, fill=(255, 100, 100), width=3)
        
        # Save dummy face
        Path(face_path).parent.mkdir(exist_ok=True)
        img.save(face_path)
        print(f"✅ Dummy face image created: {face_path}")
    
    # Test AI generation
    print("🎨 Testing AI generation...")
    result = generator.generate_dollhouse_image(face_path, test_card_info)
    
    if result.get('success'):
        print("✅ AI Generation Test PASSED!")
        print(f"   Generated images: {len(result.get('image_paths', []))}")
        print(f"   First image: {result.get('image_path')}")
        print(f"   API used: {result.get('api_used')}")
        print(f"   Quality score: {result.get('quality_score')}")
        
        # Check if files exist
        for img_path in result.get('image_paths', []):
            if Path(img_path).exists():
                print(f"   ✅ File exists: {img_path}")
            else:
                print(f"   ❌ File missing: {img_path}")
        
        return True
    else:
        print("❌ AI Generation Test FAILED!")
        print(f"   Error: {result.get('error')}")
        return False

def test_web_api():
    """Test web API endpoints"""
    print("\n🌐 Testing Web API...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # Test session status
        response = requests.get(f"{base_url}/get_session_status")
        if response.status_code == 200:
            print("✅ Session status endpoint working")
            print(f"   Status: {response.json()}")
        else:
            print(f"❌ Session status failed: {response.status_code}")
        
        # Test reset session
        response = requests.post(f"{base_url}/reset_session")
        if response.status_code == 200:
            print("✅ Reset session endpoint working")
        else:
            print(f"❌ Reset session failed: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to web server. Make sure app.py is running.")
        return False
    except Exception as e:
        print(f"❌ Web API test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AI Card Visit Generator - Test Suite")
    print("=" * 50)
    
    # Test 1: AI Generation
    ai_test_passed = test_ai_generation()
    
    # Test 2: Web API
    web_test_passed = test_web_api()
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   AI Generation: {'✅ PASSED' if ai_test_passed else '❌ FAILED'}")
    print(f"   Web API: {'✅ PASSED' if web_test_passed else '❌ FAILED'}")
    
    if ai_test_passed and web_test_passed:
        print("\n🎉 All tests PASSED! The application is working correctly.")
        print("\n📝 Next steps:")
        print("   1. Open browser to http://127.0.0.1:5000")
        print("   2. Chụp ảnh name card và khuôn mặt")
        print("   3. Nhấn 'Tạo Ảnh AI Dollhouse'")
        print("   4. Xem kết quả trên màn hình result")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
