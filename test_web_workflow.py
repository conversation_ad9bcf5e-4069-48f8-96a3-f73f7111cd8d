#!/usr/bin/env python3
"""
Test complete web workflow của AI Card Visit Generator
"""

import requests
import json
import time
from pathlib import Path

def test_complete_web_workflow():
    """Test complete web workflow"""
    print("🌐 Testing Complete Web Workflow")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # Step 1: Reset session
        print("🔄 Step 1: Reset session...")
        response = requests.post(f"{base_url}/reset_session")
        if response.status_code == 200:
            print("✅ Session reset successful")
        else:
            print(f"❌ Session reset failed: {response.status_code}")
            return False
        
        # Step 2: Check initial status
        print("\n📊 Step 2: Check initial status...")
        response = requests.get(f"{base_url}/get_session_status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Initial status: {status['status']}")
            print(f"   Has card: {status['has_card']}")
            print(f"   Has face: {status['has_face']}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
        
        # Step 3: Simulate capture card (camera 0)
        print("\n📸 Step 3: Simulate capture card...")
        response = requests.post(f"{base_url}/capture_step/0")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Card capture successful: {result.get('image_path')}")
        else:
            print(f"❌ Card capture failed: {response.status_code}")
            return False
        
        # Step 4: Simulate capture face (camera 1)
        print("\n👤 Step 4: Simulate capture face...")
        response = requests.post(f"{base_url}/capture_step/1")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Face capture successful: {result.get('image_path')}")
        else:
            print(f"❌ Face capture failed: {response.status_code}")
            return False
        
        # Step 5: Check status after captures
        print("\n📊 Step 5: Check status after captures...")
        response = requests.get(f"{base_url}/get_session_status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Status after captures: {status['status']}")
            print(f"   Has card: {status['has_card']}")
            print(f"   Has face: {status['has_face']}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
        
        # Step 6: Process images (OCR + AI generation)
        print("\n🎨 Step 6: Process images (OCR + AI generation)...")
        print("   This may take 30-60 seconds...")
        
        start_time = time.time()
        response = requests.post(f"{base_url}/process_images")
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Image processing successful!")
            print(f"   Processing time: {end_time - start_time:.1f} seconds")
            print(f"   Card info: {result.get('card_info', {}).get('name', 'N/A')}")
            print(f"   Generated images: {len(result.get('generated_images', []))}")
            
            # Check if generated images exist
            for i, img_path in enumerate(result.get('generated_images', []), 1):
                img_url = f"{base_url}/{img_path}"
                img_response = requests.get(img_url)
                if img_response.status_code == 200:
                    print(f"   ✅ Image {i} accessible: {img_path}")
                else:
                    print(f"   ❌ Image {i} not accessible: {img_path}")
            
        else:
            print(f"❌ Image processing failed: {response.status_code}")
            try:
                error_info = response.json()
                print(f"   Error: {error_info.get('message', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
        
        # Step 7: Test result page
        print("\n📄 Step 7: Test result page...")
        response = requests.get(f"{base_url}/result")
        if response.status_code == 200:
            print("✅ Result page accessible")
        else:
            print(f"❌ Result page failed: {response.status_code}")
            return False
        
        # Step 8: Final status check
        print("\n📊 Step 8: Final status check...")
        response = requests.get(f"{base_url}/get_session_status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Final status: {status['status']}")
            print(f"   Generated images: {len(status.get('generated_images', []))}")
        else:
            print(f"❌ Final status check failed: {response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to web server.")
        print("   Please make sure 'python app.py' is running in another terminal.")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_image_download():
    """Test image download functionality"""
    print("\n💾 Testing Image Download...")
    
    base_url = "http://127.0.0.1:5000"
    
    # Get latest generated images
    outputs_dir = Path("outputs")
    if not outputs_dir.exists():
        print("❌ Outputs directory not found")
        return False
    
    image_files = list(outputs_dir.glob("*.png"))
    if not image_files:
        print("❌ No generated images found")
        return False
    
    # Test downloading the latest image
    latest_image = max(image_files, key=lambda x: x.stat().st_mtime)
    image_url = f"{base_url}/outputs/{latest_image.name}"
    
    try:
        response = requests.get(image_url)
        if response.status_code == 200:
            print(f"✅ Image download successful: {latest_image.name}")
            print(f"   File size: {len(response.content)} bytes")
            print(f"   Content type: {response.headers.get('content-type', 'unknown')}")
            return True
        else:
            print(f"❌ Image download failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Download test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AI Card Visit Generator - Complete Web Workflow Test")
    print("=" * 60)
    
    # Test complete workflow
    workflow_success = test_complete_web_workflow()
    
    # Test image download
    download_success = test_image_download()
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   Complete Workflow: {'✅ PASSED' if workflow_success else '❌ FAILED'}")
    print(f"   Image Download: {'✅ PASSED' if download_success else '❌ FAILED'}")
    
    if workflow_success and download_success:
        print("\n🎉 ALL TESTS PASSED! The application is fully functional.")
        print("\n🎯 Ready for production use:")
        print("   • Camera streaming: ✅ Working")
        print("   • Image capture: ✅ Working")
        print("   • OCR processing: ✅ Working")
        print("   • AI generation: ✅ Working")
        print("   • Result display: ✅ Working")
        print("   • Image download: ✅ Working")
        
        print("\n📱 User Experience:")
        print("   1. Open http://127.0.0.1:5000")
        print("   2. Chụp name card với camera Logitech C270")
        print("   3. Chụp khuôn mặt với camera laptop")
        print("   4. Nhấn 'Tạo Ảnh AI Dollhouse'")
        print("   5. Xem kết quả và tải về ảnh")
        
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
        print("   Make sure the Flask app is running: python app.py")

if __name__ == "__main__":
    main()
