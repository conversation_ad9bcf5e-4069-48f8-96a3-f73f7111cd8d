# import cv2
# from flask import Flask, render_template, Response, request, jsonify
# import threading
# import time
# import os

# app = Flask(__name__)

# UPLOAD_FOLDER = 'static/img'
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)

# camera_0 = None  # Logitech (bên trái)
# camera_1 = None  # Laptop (bên ph<PERSON>i)
# output_frame_0 = None
# output_frame_1 = None
# lock_0 = threading.Lock()
# lock_1 = threading.Lock()

# initial_focus_value = 0  # Logitech manual focus

# def initialize_camera(camera_index, focus_value=None):
#     cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
#     if not cap.isOpened():
#         print(f"Không thể mở camera {camera_index}")
#         return None

#     cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
#     cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

#     if focus_value is not None:
#         cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
#         cap.set(cv2.CAP_PROP_FOCUS, focus_value)
#         print(f"Camera {camera_index} - Focus set to {focus_value}")

#     return cap

# def generate_frames(camera_id):
#     global camera_0, camera_1, output_frame_0, output_frame_1

#     while True:
#         cap = camera_0 if camera_id == 0 else camera_1
#         lock = lock_0 if camera_id == 0 else lock_1

#         if cap is None or not cap.isOpened():
#             print(f"Camera {camera_id} chưa sẵn sàng.")
#             time.sleep(2)
#             continue

#         ret, frame = cap.read()
#         if not ret:
#             print(f"Lỗi đọc camera {camera_id}. Đang khởi động lại...")
#             cap.release()
#             if camera_id == 0:
#                 with lock_0:
#                     camera_0 = initialize_camera(0, initial_focus_value)
#             else:
#                 with lock_1:
#                     camera_1 = initialize_camera(1)
#             time.sleep(1)
#             continue

#         with lock:
#             if camera_id == 0:
#                 output_frame_0 = frame.copy()
#             else:
#                 output_frame_1 = frame.copy()

#         ret, buffer = cv2.imencode('.jpg', frame)
#         frame = buffer.tobytes()
#         yield (b'--frame\r\n'
#                b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

# @app.route('/')
# def index():
#     return render_template('index.html')

# @app.route('/video_feed/<int:camera_id>')
# def video_feed(camera_id):
#     return Response(generate_frames(camera_id),
#                     mimetype='multipart/x-mixed-replace; boundary=frame')

# @app.route('/capture_step/<int:camera_id>', methods=['POST'])
# def capture_step(camera_id):
#     global output_frame_0, output_frame_1

#     timestamp = int(time.time())
#     filename = None

#     if camera_id == 0:
#         with lock_0:
#             if output_frame_0 is not None:
#                 filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
#                 cv2.imwrite(filename, output_frame_0)
#     elif camera_id == 1:
#         with lock_1:
#             if output_frame_1 is not None:
#                 filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
#                 cv2.imwrite(filename, output_frame_1)

#     if filename:
#         return jsonify({'status': 'success', 'image_path': filename})
#     else:
#         return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500

# @app.route('/adjust_focus', methods=['POST'])
# def adjust_focus():
#     global camera_0, initial_focus_value
#     data = request.get_json()
#     new_focus_value = data.get('focus_value')

#     if new_focus_value is None:
#         return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

#     try:
#         new_focus_value = int(new_focus_value)
#     except ValueError:
#         return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

#     with lock_0:
#         if camera_0 and camera_0.isOpened():
#             camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
#             initial_focus_value = new_focus_value
#             print(f"Focus mới cho Logitech: {new_focus_value}")
#             return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
#         else:
#             return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

# def setup_cameras():
#     global camera_0, camera_1
#     print("Đang khởi tạo camera...")
#     camera_0 = initialize_camera(0, initial_focus_value)  # Logitech
#     camera_1 = initialize_camera(1)  # Laptop

#     threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
#     threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

# if __name__ == '__main__':
#     with app.app_context():
#         setup_cameras()
#     app.run(host='0.0.0.0', port=5000, debug=False)



import cv2
from flask import Flask, render_template, Response, request, jsonify, redirect, url_for, send_from_directory
import threading
import time
import os

# Import services
from gemini_ocr_service import GeminiOCRService
from ai_generator_simple import AIImageGenerator

app = Flask(__name__)
UPLOAD_FOLDER = 'static/img'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Camera variables
camera_0 = None  # Logitech (bên trái)
camera_1 = None  # Laptop (bên phải)
output_frame_0 = None
output_frame_1 = None
captured_frame_0 = None  # Lưu ảnh đã chụp
captured_frame_1 = None  # Lưu ảnh đã chụp
lock_0 = threading.Lock()
lock_1 = threading.Lock()
initial_focus_value = 0

# Session variables
current_session = {
    'card_image': None,
    'face_image': None,
    'card_info': None,
    'generated_images': None,
    'status': 'ready'  # ready, processing, completed
}

# Initialize services
ocr_service = GeminiOCRService()
ai_generator = AIImageGenerator()

def initialize_camera(camera_index, focus_value=None):
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_index}")
        return None

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 60)  # Yêu cầu 60 FPS

    actual_fps = cap.get(cv2.CAP_PROP_FPS)
    print(f"Camera {camera_index} - FPS thực tế: {actual_fps:.2f}")

    if focus_value is not None:
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
        cap.set(cv2.CAP_PROP_FOCUS, focus_value)
        print(f"Camera {camera_index} - Focus set to {focus_value}")

    return cap

def generate_frames(camera_id):
    global camera_0, camera_1, output_frame_0, output_frame_1

    frame_count = 0
    start_time = time.time()

    while True:
        cap = camera_0 if camera_id == 0 else camera_1
        lock = lock_0 if camera_id == 0 else lock_1

        if cap is None or not cap.isOpened():
            print(f"Camera {camera_id} chưa sẵn sàng.")
            time.sleep(2)
            continue

        ret, frame = cap.read()
        if not ret:
            print(f"Lỗi đọc camera {camera_id}. Đang khởi động lại...")
            cap.release()
            if camera_id == 0:
                with lock_0:
                    camera_0 = initialize_camera(0, initial_focus_value)
            else:
                with lock_1:
                    camera_1 = initialize_camera(1)
            time.sleep(1)
            continue

        with lock:
            if camera_id == 0:
                output_frame_0 = frame.copy()
            else:
                output_frame_1 = frame.copy()

        # Đo FPS mỗi giây
        frame_count += 1
        elapsed = time.time() - start_time
        if elapsed >= 1.0:
            fps = frame_count / elapsed
            print(f"[Camera {camera_id}] FPS hiện tại: {fps:.2f}")
            frame_count = 0
            start_time = time.time()

        ret, buffer = cv2.imencode('.jpg', frame)
        frame = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture_step/<int:camera_id>', methods=['POST'])
def capture_step(camera_id):
    global output_frame_0, output_frame_1, captured_frame_0, captured_frame_1, current_session
    timestamp = int(time.time())
    filename = None

    if camera_id == 0:
        with lock_0:
            if output_frame_0 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'card_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_0)
                captured_frame_0 = output_frame_0.copy()
                current_session['card_image'] = filename
    elif camera_id == 1:
        with lock_1:
            if output_frame_1 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'face_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_1)
                captured_frame_1 = output_frame_1.copy()
                current_session['face_image'] = filename

    if filename:
        return jsonify({'status': 'success', 'image_path': filename})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    global camera_0, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_0:
        if camera_0 and camera_0.isOpened():
            camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Focus mới cho Logitech: {new_focus_value}")
            return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

def setup_cameras():
    global camera_0, camera_1
    print("Đang khởi tạo camera...")
    camera_0 = initialize_camera(0, initial_focus_value)
    camera_1 = initialize_camera(1)

    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

# New routes for OCR and AI generation
@app.route('/process_images', methods=['POST'])
def process_images():
    global current_session

    try:
        # Check if both images are captured
        if not current_session.get('card_image') or not current_session.get('face_image'):
            return jsonify({
                'status': 'error',
                'message': 'Vui lòng chụp cả 2 ảnh trước khi xử lý'
            }), 400

        current_session['status'] = 'processing'

        # Step 1: OCR the card
        print("🔍 Starting OCR process...")
        card_info = ocr_service.extract_text_from_card(current_session['card_image'])
        current_session['card_info'] = card_info

        # Step 2: Generate AI images
        print("🎨 Starting AI generation...")
        ai_result = ai_generator.generate_dollhouse_image(
            current_session['face_image'],
            card_info
        )

        if ai_result.get('success'):
            current_session['generated_images'] = ai_result.get('image_paths', [ai_result.get('image_path')])
            current_session['status'] = 'completed'

            return jsonify({
                'status': 'success',
                'card_info': card_info,
                'generated_images': current_session['generated_images'],
                'message': 'Xử lý thành công!'
            })
        else:
            current_session['status'] = 'error'
            return jsonify({
                'status': 'error',
                'message': f'Lỗi tạo ảnh AI: {ai_result.get("error", "Unknown error")}'
            }), 500

    except Exception as e:
        current_session['status'] = 'error'
        print(f"❌ Process error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Lỗi xử lý: {str(e)}'
        }), 500

@app.route('/result')
def result():
    """Màn hình kết quả"""
    if current_session.get('status') != 'completed':
        return redirect(url_for('index'))

    return render_template('result.html',
                         card_info=current_session.get('card_info'),
                         generated_images=current_session.get('generated_images'),
                         card_image=current_session.get('card_image'),
                         face_image=current_session.get('face_image'))

@app.route('/reset_session', methods=['POST'])
def reset_session():
    """Reset session để bắt đầu lại"""
    global current_session, captured_frame_0, captured_frame_1
    current_session = {
        'card_image': None,
        'face_image': None,
        'card_info': None,
        'generated_images': None,
        'status': 'ready'
    }
    captured_frame_0 = None
    captured_frame_1 = None
    return jsonify({'status': 'success', 'message': 'Session đã được reset'})

@app.route('/get_session_status')
def get_session_status():
    """Get current session status"""
    return jsonify({
        'status': current_session.get('status', 'ready'),
        'has_card': bool(current_session.get('card_image')),
        'has_face': bool(current_session.get('face_image')),
        'card_info': current_session.get('card_info'),
        'generated_images': current_session.get('generated_images')
    })

@app.route('/outputs/<filename>')
def serve_output_file(filename):
    """Serve files from outputs directory"""
    return send_from_directory('outputs', filename)

if __name__ == '__main__':
    with app.app_context():
        setup_cameras()
    app.run(host='0.0.0.0', port=5000, debug=False)
